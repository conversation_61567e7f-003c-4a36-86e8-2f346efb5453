import os
import json

# Set input and output directories
input_dir = "./ctg-studies"  # Update this to where your JSON files are
output_dir = "./processed"
os.makedirs(output_dir, exist_ok=True)

# Helper function to extract nested values safely
def get_nested(d, keys, default=""):
    for key in keys:
        d = d.get(key, {})
    return d if isinstance(d, str) else json.dumps(d, indent=2) if d else default

# Function to convert a trial JSON into a flattened text block
def process_trial_json(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    protocol = data.get("protocolSection", {})

    def section(title, content):
        return f"## {title}\n{content.strip() if content else 'N/A'}\n"

    output = []
    output.append(f"# Title: {get_nested(protocol, ['identificationModule', 'briefTitle'])}")
    output.append(section("NCT ID", get_nested(protocol, ['identificationModule', 'nctId'])))
    output.append(section("Study Phase", ", ".join(protocol.get("designModule", {}).get("phases", []))))
    output.append(section("Conditions", "\n".join(protocol.get("conditionsModule", {}).get("conditions", []))))
    
    interventions = protocol.get("armsInterventionsModule", {}).get("interventions", [])
    output.append(section("Interventions", "\n".join(i.get("name", "") for i in interventions)))

    output.append(section("Summary", get_nested(protocol, ['descriptionModule', 'briefSummary'])))
    output.append(section("Detailed Description", get_nested(protocol, ['descriptionModule', 'detailedDescription'])))
    output.append(section("Eligibility Criteria", get_nested(protocol, ['eligibilityModule', 'eligibilityCriteria'])))

    locations = protocol.get("contactsLocationsModule", {}).get("locations", [])
    location_text = "\n".join(
        f"- {loc.get('facility', '')}, {loc.get('city', '')}, {loc.get('state', '')}, {loc.get('country', '')}"
        for loc in locations
    )
    output.append(section("Study Locations", location_text))

    references = protocol.get("referencesModule", {}).get("references", [])
    ref_text = "\n".join(
        f"- PMID: {ref.get('pmid', 'N/A')} - {ref.get('citation', '')}"
        for ref in references
    )
    output.append(section("References", ref_text))

    return "\n".join(output)

# Process all JSON files in the input directory
for filename in os.listdir(input_dir):
    if filename.endswith(".json"):
        input_path = os.path.join(input_dir, filename)
        output_text = process_trial_json(input_path)
        output_filename = os.path.splitext(filename)[0] + ".txt"
        with open(os.path.join(output_dir, output_filename), "w", encoding="utf-8") as out_file:
            out_file.write(output_text)

print(f"Processed {len(os.listdir(output_dir))} files into: {output_dir}")
